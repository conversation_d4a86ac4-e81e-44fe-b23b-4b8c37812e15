/* 
 * Status Badge Component Styles
 * Additional responsive utilities for the status badge component
 */

/* Base status badge styles */
.status-badge-base {
  @apply inline-flex items-center justify-center rounded-full font-medium transition-all duration-200 ease-in-out;
}

/* Responsive text truncation utilities */
.status-badge-text-responsive {
  @apply truncate max-w-[6rem] sm:max-w-[8rem] md:max-w-none;
}

/* Icon-only responsive behavior */
.status-badge-icon-only {
  @apply hidden sm:inline;
}

/* Size variants */
.status-badge-sm {
  @apply text-xs py-0.5 px-2 gap-1;
}

.status-badge-sm .status-badge-icon {
  @apply w-3 h-3;
}

.status-badge-default {
  @apply text-xs sm:text-sm py-1 px-2 sm:px-3 gap-1 sm:gap-1.5;
}

.status-badge-default .status-badge-icon {
  @apply w-3 h-3 sm:w-4 sm:h-4;
}

.status-badge-lg {
  @apply text-sm md:text-base py-1.5 px-3 md:px-4 gap-1.5 md:gap-2;
}

.status-badge-lg .status-badge-icon {
  @apply w-4 h-4 md:w-5 md:h-5;
}

/* Color variants with improved contrast */
.status-badge-green {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.status-badge-red {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.status-badge-orange {
  @apply bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200;
}

.status-badge-blue {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.status-badge-yellow {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.status-badge-purple {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200;
}

.status-badge-gray {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200;
}

/* Hover effects for interactive badges */
.status-badge-interactive {
  @apply cursor-pointer hover:shadow-sm hover:scale-105 active:scale-95;
}

/* Focus states for accessibility */
.status-badge-focusable {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

/* Animation for status changes */
.status-badge-animated {
  @apply transition-all duration-300 ease-in-out;
}

/* Pulse animation for pending/loading states */
.status-badge-pulse {
  @apply animate-pulse;
}

/* Mobile-first responsive utilities */
@media (max-width: 640px) {
  .status-badge-mobile-compact {
    @apply px-1.5 py-0.5 text-xs gap-1;
  }
  
  .status-badge-mobile-compact .status-badge-icon {
    @apply w-3 h-3;
  }
  
  .status-badge-mobile-icon-only .status-badge-text {
    @apply hidden;
  }
}

/* Tablet responsive utilities */
@media (min-width: 641px) and (max-width: 1024px) {
  .status-badge-tablet {
    @apply px-2.5 py-1 text-sm gap-1.5;
  }
  
  .status-badge-tablet .status-badge-icon {
    @apply w-4 h-4;
  }
}

/* Desktop responsive utilities */
@media (min-width: 1025px) {
  .status-badge-desktop {
    @apply px-3 py-1 text-sm gap-1.5;
  }
  
  .status-badge-desktop .status-badge-icon {
    @apply w-4 h-4;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .status-badge-base {
    @apply border border-current;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .status-badge-animated {
    @apply transition-none;
  }
  
  .status-badge-pulse {
    @apply animate-none;
  }
}

/* Print styles */
@media print {
  .status-badge-base {
    @apply bg-transparent border border-current text-current;
  }
}

/* Container query support for future use */
@container (max-width: 300px) {
  .status-badge-container-sm {
    @apply px-1 py-0.5 text-xs;
  }
  
  .status-badge-container-sm .status-badge-text {
    @apply hidden;
  }
}

/* Utility classes for common patterns */
.status-badge-table-cell {
  @apply status-badge-base status-badge-default;
}

.status-badge-card {
  @apply status-badge-base status-badge-sm;
}

.status-badge-header {
  @apply status-badge-base status-badge-lg;
}
