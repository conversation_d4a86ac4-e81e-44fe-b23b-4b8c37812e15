@import './design-system.css';
@import './app/table.css';
@import './app/order.css';
@import './admin/order.css';
@import './components/status-badge.css';

/*********************************/
/*     CSS Standard Classes      */
/*********************************/


/*Headings*/
.heading-one {}

.heading-two {}

.heading-three {}


/*Paragraph*/
.paragraph {
  @apply leading-normal;
}


button {
  @apply hover:cursor-pointer;
}






/*********************************/
/*        Custom CSS             */
/*********************************/
body {
  font-family: 'Inter', 'system-ui', 'Roboto';
}



/*-----------LEFT MENU------------*/

.left-top-level-menu-section {
  @apply flex flex-col mt-4;
}

.menu-link-head {
  @apply flex items-center text-base cursor-pointer py-1 pl-3 font-semibold rounded-lg hover:bg-background;
}

.sub-menu-left {
  @apply flex flex-col;
}

.sub-menu-left-link {
  @apply transition-colors text-sm font-medium ml-4 px-6 py-1.5 rounded-lg hover:bg-background;
}

.icon-left-menu {
  @apply mr-2;
}

.icon-left-menu>svg {
  width: 17.5px;
  height: auto;
}



/*-----------TOP MENU BAR------------*/

.top-menu-main-link {
  @apply inline-flex items-center justify-center px-2.5 py-1 mx-2 text-sm font-medium transition-colors rounded-md text-muted-foreground hover:text-primary focus:outline-none disabled:opacity-50 disabled:pointer-events-none hover:bg-accent w-max;
}

.top-menu-sub-link {
  @apply flex p-4 text-sm rounded hover:bg-neutral-100 border-b border-neutral-100 hover:text-blue-800;
}

.top-menu-sub-link-label {
  @apply block mb-1 font-medium text-sm leading-5 hover:text-primary;
}

.top-menu-sub-link-description {
  @apply text-muted-foreground text-xs opacity-90 leading-5;
}

.icon-top-menu-sub-link {
  @apply mr-5;
}

.icon-top-menu-sub-link>svg {
  width: 30px;
  height: auto;
  color: hsl(var(--muted-foreground));
  stroke-width: 1.6;
}

.activeMenuLink {
  @apply text-white;
  @apply bg-orange-600 hover:bg-orange-700 hover:text-white;
}




/*-----------SEARCH TOP------------*/
.search-top {
  @apply  flex w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 h-9 md:w-[100px] lg:w-[300px];
}



.number-stats {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-green-400 via-blue-500 to-purple-500;
}







[x-cloak] {
  display: none;
}



/* Filters Div Section */
.filter-options {
  @apply grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 mt-4;
}


.select-filters-options {
  @apply px-4 py-3 w-full rounded-md bg-gray-100 border-transparent focus:border-gray-500 focus:bg-white focus:ring-0 text-sm;
}


#reset-all-filters-button {
  @apply px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 text-sm font-medium rounded-md;
}




#inner-body {
  @apply pb-10;
}


.box-section-wrapper {
  @apply bg-white py-8 px-8 border-2 border-gray-200 rounded-xl mb-12 shadow-sm;
}

.box-section-filters {
  @apply bg-white py-6 px-6 border border-gray-100 rounded-xl mb-8 shadow-sm;
}


.box-section {
  @apply bg-white border border-gray-300 rounded-lg shadow-sm;
}


body {
  text-rendering: optimizeLegibility;
  scroll-behavior: smooth;
}

.app-body {
  @apply px-2 py-2 bg-gray-100;
  @apply md:px-8 md:py-2;
}

/*.app-body{
  @apply px-8 py-2 bg-gray-100;
}*/



/*admin forms*/

.admin-input-field {
  @apply bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white;
}


/* Table Progress Bar */


.progress-wrap {
  height: 6px;
}

.progressbar {

  color: #222;
  display: none;
  background: #ffedd5;
  background-color: #ffedd5 !important;
  background-image: linear-gradient(-45deg, #fed7aa 25%, transparent 25%, transparent 50%, #fed7aa 50%, #fed7aa 75%, transparent 75%, transparent);
  background-size: 35px 35px;
  animation: cssProgressActiveRight 2s linear infinite;
  /*      height: 6px;*/
  transition: none 0s ease 0s;
  height: 100%;
  width: 100%;

}


@keyframes cssProgressActiveRight {
  0% {
    background-position: 0 0
  }

  100% {
    background-position: -35px -35px
  }
}



.footer-link-app {
  @apply text-base px-4 py-1.5 leading-6 text-gray-500 hover:text-gray-700 dark:text-gray-400 hover:bg-gray-50 rounded-lg;
}

.footer-social-icon-single-app {
  @apply text-gray-500 hover:text-gray-700 p-2 hover:bg-gray-50 rounded-full;
}




/* Pagination Styles */
span[aria-current="page"] span {
  @apply bg-emerald-600 text-white border-emerald-600 hover:bg-emerald-700;
}



/* Notifications Panel */
#notifications-panel::-webkit-scrollbar {
  width: 4px;
}

#notifications-panel::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 2px #cbd5e1;
}

#notifications-panel::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  outline: 1px solid #f1f5f9;
}

/* @keyframes shake {

  0%,
  20% {
    transform: translateX(0);
  }

  5% {
    transform: translateX(-5px);
  }

  10% {
    transform: translateX(5px);
  }

  15% {
    transform: translateX(-5px);
  }

  20% {
    transform: translateX(5px);
  }

  20%,
  100% {
    transform: translateX(0);
  }
} */

@keyframes shake {
  0%, 100% { transform: rotate(0deg); }
  20%, 60% { transform: rotate(-15deg); }
  40%, 80% { transform: rotate(15deg); }
}

.animate-shake-twice {
  animation: shake 1s ease-in-out 2; /* 2 iterations */
}

.animate-shake {
  animation: shake 2s ease-in-out infinite;
  transform-origin: top center;
}

#notifications-panel::-webkit-scrollbar {
  width: 4px;
}

#notifications-panel::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 2px #cbd5e1;
}

#notifications-panel::-webkit-scrollbar-thumb {
  background-color: #cbd5e1;
  outline: 1px solid #f1f5f9;
}

input:disabled,
select:disabled,
textarea:disabled {
  @apply bg-gray-200 cursor-not-allowed;
}

/* .myclass{
  background-color:red;
} */

/* Selection box (input) */
.tailwind-select.select2-selection--single {
  border: 1px solid #d1d5db;
  /* border-gray-300 */
  border-radius: 0.375rem;
  /* rounded-md */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  /* shadow-sm */
  font-size: 0.875rem;
  /* text-sm */
  height: 2.5rem;
  padding: 0.25rem 0.75rem;
  display: flex;
  align-items: center;
}

/* Rendered text */
.tailwind-select .select2-selection__rendered {
  color: #111827;
  /* text-gray-900 */
  line-height: 1.75rem;
  padding-right: 1.5rem;
}

/* Arrow */
.tailwind-select .select2-selection__arrow {
  top: 0.5rem;
  right: 0.75rem;
  width: 1rem;
  height: 1rem;
}

/* Dropdown */
.tailwind-dropdown {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  background-color: white;
  font-size: 0.875rem;
  z-index: 9999;
}

/* Dropdown highlight */
.tailwind-dropdown .select2-results__option--highlighted {
  background-color: #4f46e5;
  /* indigo-600 */
  color: white;
}

/* Selected option in dropdown */
.tailwind-dropdown .select2-results__option--selected {
  background-color: #e0e7ff;
  /* indigo-100 */
  color: #3730a3;
  /* indigo-800 */
}

.select2-selection__rendered {
  padding: 0px !important;
}

.paper-shadow {
  box-shadow: rgba(0, 0, 0, 0.25) 0px 20px 10px -20px;
}