@props([
'item' => null,
'size' => 'default', // 'sm', 'default', 'lg'
'iconOnly' => false, // Show only icon on very small screens
'responsive' => true, // Enable responsive behavior
])

@php
$stateLabel = $item->state_label ?? 'Pending';
$color = $item->badge_color ?? 'orange';
$icon = $item->badge_icon ?? 'triangle-alert';

// Size variants
$sizeClasses = [
    'sm' => [
        'text' => 'text-xs',
        'padding' => 'py-0.5 px-2',
        'icon' => 'w-3 h-3',
        'gap' => 'gap-1'
    ],
    'default' => [
        'text' => 'text-xs sm:text-sm',
        'padding' => 'py-1 px-2 sm:px-3',
        'icon' => 'w-3 h-3 sm:w-4 sm:h-4',
        'gap' => 'gap-1 sm:gap-1.5'
    ],
    'lg' => [
        'text' => 'text-sm md:text-base',
        'padding' => 'py-1.5 px-3 md:px-4',
        'icon' => 'w-4 h-4 md:w-5 md:h-5',
        'gap' => 'gap-1.5 md:gap-2'
    ]
];

$currentSize = $sizeClasses[$size] ?? $sizeClasses['default'];

// Responsive classes
$responsiveClasses = $responsive ? 'max-w-full' : '';

// Icon-only mode for very small screens
$textClasses = $iconOnly ? 'hidden sm:inline' : '';
@endphp

<span
  class="inline-flex items-center justify-center {{ $currentSize['gap'] }} {{ $currentSize['padding'] }}
         rounded-full {{ $currentSize['text'] }} font-medium {{ $responsiveClasses }}
         bg-{{ $color }}-100 text-{{ $color }}-800
         dark:bg-{{ $color }}-900 dark:text-{{ $color }}-200
         transition-all duration-200 ease-in-out
         {{ $responsive ? 'min-w-0' : '' }}">

  @if($icon)
    @component("components.icons.lucide.{$icon}", [
      'class' => $currentSize['icon'] . ' stroke-2 flex-shrink-0'
    ])
    @endcomponent
  @endif

  <span class="truncate {{ $textClasses }} {{ $responsive ? 'max-w-[6rem] sm:max-w-[8rem] md:max-w-none' : '' }}">
    {{ $stateLabel }}
  </span>
</span>