{{-- 
  Status Badge Component Examples
  This file demonstrates the improved responsive status badge usage
--}}

<div class="p-6 space-y-8">
    <h2 class="text-2xl font-bold mb-6">Improved Status Badge Examples</h2>
    
    {{-- Default Usage --}}
    <div class="space-y-4">
        <h3 class="text-lg font-semibold">Default Responsive Badge</h3>
        <div class="flex flex-wrap gap-4">
            <x-ui.badges.order.statusBadge :item="$item" />
        </div>
        <p class="text-sm text-gray-600">
            Automatically adjusts size and spacing based on screen size. Text truncates on small screens.
        </p>
    </div>

    {{-- Size Variants --}}
    <div class="space-y-4">
        <h3 class="text-lg font-semibold">Size Variants</h3>
        <div class="flex flex-wrap items-center gap-4">
            <div class="space-y-2">
                <p class="text-xs text-gray-500">Small</p>
                <x-ui.badges.order.statusBadge :item="$item" size="sm" />
            </div>
            <div class="space-y-2">
                <p class="text-xs text-gray-500">Default</p>
                <x-ui.badges.order.statusBadge :item="$item" size="default" />
            </div>
            <div class="space-y-2">
                <p class="text-xs text-gray-500">Large</p>
                <x-ui.badges.order.statusBadge :item="$item" size="lg" />
            </div>
        </div>
    </div>

    {{-- Icon Only Mode --}}
    <div class="space-y-4">
        <h3 class="text-lg font-semibold">Icon-Only Mode (Mobile)</h3>
        <div class="flex flex-wrap gap-4">
            <x-ui.badges.order.statusBadge :item="$item" :iconOnly="true" />
        </div>
        <p class="text-sm text-gray-600">
            Shows only icon on small screens, text appears on larger screens. Perfect for mobile tables.
        </p>
    </div>

    {{-- Non-Responsive Mode --}}
    <div class="space-y-4">
        <h3 class="text-lg font-semibold">Fixed Size (Non-Responsive)</h3>
        <div class="flex flex-wrap gap-4">
            <x-ui.badges.order.statusBadge :item="$item" :responsive="false" />
        </div>
        <p class="text-sm text-gray-600">
            Maintains consistent size across all screen sizes.
        </p>
    </div>

    {{-- Table Usage Example --}}
    <div class="space-y-4">
        <h3 class="text-lg font-semibold">Table Usage (Recommended)</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full border border-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left">Order ID</th>
                        <th class="px-4 py-2 text-left">Website</th>
                        <th class="px-4 py-2 text-left">Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-t">
                        <td class="px-4 py-2">#12345</td>
                        <td class="px-4 py-2">example.com</td>
                        <td class="px-4 py-2">
                            {{-- Icon-only on mobile, full badge on larger screens --}}
                            <x-ui.badges.order.statusBadge :item="$item" :iconOnly="true" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <p class="text-sm text-gray-600">
            In tables, use iconOnly="true" for better mobile experience.
        </p>
    </div>

    {{-- Responsive Behavior Demo --}}
    <div class="space-y-4">
        <h3 class="text-lg font-semibold">Responsive Behavior</h3>
        <div class="bg-gray-100 p-4 rounded-lg">
            <p class="text-sm mb-4">Resize your browser window to see the responsive behavior:</p>
            <ul class="text-sm space-y-2 text-gray-700">
                <li><strong>Mobile (< 640px):</strong> Smaller padding, smaller icons, truncated text</li>
                <li><strong>Tablet (640px+):</strong> Medium padding, medium icons, full text</li>
                <li><strong>Desktop (768px+):</strong> Full padding, full icons, no text truncation</li>
            </ul>
        </div>
    </div>
</div>
