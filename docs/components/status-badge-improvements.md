# Status Badge Component Improvements

## Overview

The status badge component has been significantly improved to provide better responsive design and mobile experience. The new design addresses common issues with small screen displays while maintaining visual consistency across different devices.

## Key Improvements

### 1. Responsive Sizing
- **Mobile (< 640px)**: Smaller padding (`py-1 px-2`), smaller icons (`w-3 h-3`), compact text
- **Tablet (640px+)**: Medium padding (`py-1 px-3`), medium icons (`w-4 h-4`), full text
- **Desktop (768px+)**: Full padding and icons with no text truncation

### 2. Text Truncation
- Long status labels are automatically truncated on small screens
- Progressive max-width: `6rem` → `8rem` → `none` as screen size increases
- Uses CSS `truncate` class for clean ellipsis display

### 3. Icon-Only Mode
- Option to hide text on very small screens, showing only the icon
- Perfect for mobile table cells where space is limited
- Text reappears on larger screens automatically

### 4. Size Variants
- **Small (`sm`)**: Compact version for dense layouts
- **Default**: Standard responsive behavior
- **Large (`lg`)**: Prominent display for headers or important status

### 5. Accessibility Improvements
- Maintains proper contrast ratios in dark mode
- Supports high contrast mode with borders
- Respects reduced motion preferences
- Proper focus states for interactive badges

## Usage Examples

### Basic Usage (Recommended)
```blade
<x-ui.badges.order.statusBadge :item="$orderItem" />
```

### Size Variants
```blade
{{-- Small badge for compact layouts --}}
<x-ui.badges.order.statusBadge :item="$orderItem" size="sm" />

{{-- Large badge for headers --}}
<x-ui.badges.order.statusBadge :item="$orderItem" size="lg" />
```

### Icon-Only Mode (Mobile Tables)
```blade
{{-- Shows only icon on mobile, full badge on larger screens --}}
<x-ui.badges.order.statusBadge :item="$orderItem" :iconOnly="true" />
```

### Fixed Size (Non-Responsive)
```blade
{{-- Maintains consistent size across all screens --}}
<x-ui.badges.order.statusBadge :item="$orderItem" :responsive="false" />
```

## Mobile Table Implementation

For optimal mobile experience in tables, use the icon-only mode:

```blade
<table class="min-w-full">
    <thead>
        <tr>
            <th class="px-2 py-2 sm:px-4">Order</th>
            <th class="px-2 py-2 sm:px-4">Status</th>
        </tr>
    </thead>
    <tbody>
        @foreach($orders as $order)
        <tr>
            <td class="px-2 py-2 sm:px-4">#{{ $order->id }}</td>
            <td class="px-2 py-2 sm:px-4">
                <x-ui.badges.order.statusBadge 
                    :item="$order" 
                    :iconOnly="true" 
                />
            </td>
        </tr>
        @endforeach
    </tbody>
</table>
```

## Component Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `item` | Object | `null` | The model instance with status data |
| `size` | String | `'default'` | Size variant: `'sm'`, `'default'`, `'lg'` |
| `iconOnly` | Boolean | `false` | Hide text on small screens, show only icon |
| `responsive` | Boolean | `true` | Enable responsive behavior |

## CSS Classes Available

The component includes additional CSS utility classes for advanced customization:

- `.status-badge-mobile-compact`: Extra compact mobile styling
- `.status-badge-interactive`: Hover effects for clickable badges
- `.status-badge-animated`: Smooth transitions for status changes
- `.status-badge-pulse`: Pulse animation for loading states

## Migration Guide

### From Old Component
```blade
{{-- Old usage --}}
<x-ui.badges.order.statusBadge :item="$item" />

{{-- New usage (same, but with improved responsive behavior) --}}
<x-ui.badges.order.statusBadge :item="$item" />
```

### For Mobile Tables
```blade
{{-- Old: Badge might overflow on mobile --}}
<td><x-ui.badges.order.statusBadge :item="$item" /></td>

{{-- New: Icon-only on mobile, full badge on larger screens --}}
<td><x-ui.badges.order.statusBadge :item="$item" :iconOnly="true" /></td>
```

## Browser Support

- All modern browsers (Chrome, Firefox, Safari, Edge)
- Responsive design works on all screen sizes
- Graceful degradation for older browsers
- Print-friendly styling included

## Performance Considerations

- Uses CSS transitions for smooth animations
- Respects `prefers-reduced-motion` for accessibility
- Minimal JavaScript footprint (component is pure CSS/HTML)
- Optimized for mobile performance

## Future Enhancements

- Container query support for even more precise responsive behavior
- Additional animation options
- Custom color scheme support
- Integration with design system tokens
